#ifndef UITEXT_H
#define UITEXT_H

#include <SDL3/SDL.h>
#include <SDL3_ttf/SDL_ttf.h>

#define SDL_MATH_3D_IMPLEMENTATION
#include "../testgputext/SDL_math3d.h"

#define MAX_VERTEX_COUNT 4000
#define MAX_INDEX_COUNT 6000

// Vector types
typedef SDL_FPoint Vec2;

typedef struct Vec3
{
    float x, y, z;
} Vec3;

// Vertex structure for text rendering
typedef struct Vertex
{
    Vec3 pos;
    SDL_FColor colour;
    Vec2 uv;
} Vertex;

// Geometry data for text rendering
typedef struct GeometryData
{
    Vertex *vertices;
    int vertex_count;
    int *indices;
    int index_count;
} GeometryData;

// Text rendering context
typedef struct UITextContext
{
    // GPU resources
    SDL_GPUDevice *device;
    SDL_GPUGraphicsPipeline *pipeline;
    SDL_GPUBuffer *vertex_buffer;
    SDL_GPUBuffer *index_buffer;
    SDL_GPUTransferBuffer *transfer_buffer;
    SDL_GPUSampler *sampler;

    // Text rendering
    GeometryData geometry_data;
    TTF_TextEngine *engine;
    SDL_Mat4X4 projection_matrix;
    SDL_Mat4X4 model_matrices[MAX_VERTEX_COUNT / 4]; // One model matrix per text object (assuming max 4 vertices per character)
    int model_matrix_count;
    bool use_SDF;

    // Window dimensions for projection matrix
    int window_width;
    int window_height;
} UITextContext;



// Individual text object
typedef struct UITextObject
{
    TTF_Text *text;
    TTF_Font *font;
    SDL_FColor colour;
    float x, y; // Position
    float letter_spacing; // Letter spacing multiplier (1.0 = normal, 0.8 = tighter, 1.2 = looser)
    float total_width; // Total width of text object (for centering)

    // Individual geometry data to avoid interference between text objects
    GeometryData geometry_data;
    bool geometry_dirty; // Flag to indicate if geometry needs to be recalculated
} UITextObject;

// Function declarations
UITextContext* UIText_CreateContext(SDL_GPUDevice *device, SDL_Window *window, int window_width, int window_height, bool use_SDF);
void UIText_DestroyContext(UITextContext *context);

UITextObject* UIText_CreateText(UITextContext *context, TTF_Font *font, const char *text_string, float x, float y, SDL_FColor colour);
void UIText_DestroyText(UITextObject *text_obj);



// Prepare text data for rendering (call outside render pass)
void UIText_PrepareTextData(UITextContext *context, UITextObject **text_objects, int count, SDL_GPUCommandBuffer *cmd_buf);

// Render prepared text within an existing render pass (for combining with other rendering)
void UIText_RenderInRenderPass(UITextContext *context, UITextObject **text_objects, int count,
                               SDL_GPURenderPass *render_pass, SDL_GPUCommandBuffer *cmd_buf);

// Utility functions
void UIText_SetTextPosition(UITextObject *text_obj, float x, float y);
void UIText_SetTextColour(UITextObject *text_obj, SDL_FColor colour);
void UIText_SetLetterSpacing(UITextObject *text_obj, float spacing);
void UIText_UpdateProjectionMatrix(UITextContext *context, int window_width, int window_height);

#endif // UITEXT_H