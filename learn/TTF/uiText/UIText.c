#include "UIText.h"
#include <float.h>

// Shaders
#include "../testgputext/shaders/shader.vert.spv.h"
#include "../testgputext/shaders/shader.frag.spv.h"
#include "../testgputext/shaders/shader-sdf.frag.spv.h"
#include "../testgputext/shaders/shader.vert.dxil.h"
#include "../testgputext/shaders/shader.frag.dxil.h"
#include "../testgputext/shaders/shader-sdf.frag.dxil.h"
#include "../testgputext/shaders/shader.vert.msl.h"
#include "../testgputext/shaders/shader.frag.msl.h"
#include "../testgputext/shaders/shader-sdf.frag.msl.h"

#define SUPPORTED_SHADER_FORMATS (SDL_GPU_SHADERFORMAT_SPIRV | SDL_GPU_SHADERFORMAT_DXIL | SDL_GPU_SHADERFORMAT_MSL)

typedef enum
{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>l<PERSON>hader,
    <PERSON>xel<PERSON>hader_SDF,
} Shader;

// Error checking helpers


static void *check_error_ptr(void *ptr)
{
    if (!ptr)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "%s", SDL_GetError());
    }
    return ptr;
}

// Shader loading function
static SDL_GPUShader *load_shader(
        SDL_GPUDevice *device,
        Shader shader,
        Uint32 sampler_count,
        Uint32 uniform_buffer_count,
        Uint32 storage_buffer_count,
        Uint32 storage_texture_count)
{
    SDL_GPUShaderCreateInfo createinfo;
    createinfo.num_samplers = sampler_count;
    createinfo.num_storage_buffers = storage_buffer_count;
    createinfo.num_storage_textures = storage_texture_count;
    createinfo.num_uniform_buffers = uniform_buffer_count;
    createinfo.props = 0;

    SDL_GPUShaderFormat format = SDL_GetGPUShaderFormats(device);
    if (format & SDL_GPU_SHADERFORMAT_DXIL)
    {
        createinfo.format = SDL_GPU_SHADERFORMAT_DXIL;
        switch (shader)
        {
        case VertexShader:
            createinfo.code = shader_vert_dxil;
            createinfo.code_size = shader_vert_dxil_len;
            createinfo.entrypoint = "VSMain";
            break;
        case PixelShader:
            createinfo.code = shader_frag_dxil;
            createinfo.code_size = shader_frag_dxil_len;
            createinfo.entrypoint = "PSMain";
            break;
        case PixelShader_SDF:
            createinfo.code = shader_sdf_frag_dxil;
            createinfo.code_size = shader_sdf_frag_dxil_len;
            createinfo.entrypoint = "PSMain";
            break;
        }
    }
    else if (format & SDL_GPU_SHADERFORMAT_MSL)
    {
        createinfo.format = SDL_GPU_SHADERFORMAT_MSL;
        switch (shader)
        {
        case VertexShader:
            createinfo.code = shader_vert_msl;
            createinfo.code_size = shader_vert_msl_len;
            createinfo.entrypoint = "main0";
            break;
        case PixelShader:
            createinfo.code = shader_frag_msl;
            createinfo.code_size = shader_frag_msl_len;
            createinfo.entrypoint = "main0";
            break;
        case PixelShader_SDF:
            createinfo.code = shader_sdf_frag_msl;
            createinfo.code_size = shader_sdf_frag_msl_len;
            createinfo.entrypoint = "main0";
            break;
        }
    }
    else
    {
        createinfo.format = SDL_GPU_SHADERFORMAT_SPIRV;
        switch (shader)
        {
        case VertexShader:
            createinfo.code = shader_vert_spv;
            createinfo.code_size = shader_vert_spv_len;
            createinfo.entrypoint = "main";
            break;
        case PixelShader:
            createinfo.code = shader_frag_spv;
            createinfo.code_size = shader_frag_spv_len;
            createinfo.entrypoint = "main";
            break;
        case PixelShader_SDF:
            createinfo.code = shader_sdf_frag_spv;
            createinfo.code_size = shader_sdf_frag_spv_len;
            createinfo.entrypoint = "main";
            break;
        }
    }

    if (shader == VertexShader)
    {
        createinfo.stage = SDL_GPU_SHADERSTAGE_VERTEX;
    }
    else
    {
        createinfo.stage = SDL_GPU_SHADERSTAGE_FRAGMENT;
    }
    return SDL_CreateGPUShader(device, &createinfo);
}





//Copies accumulated text geometry data from CPU memory to GPU transfer buffer.
static void set_geometry_data(UITextContext *context)
{
    Vertex *transfer_data = SDL_MapGPUTransferBuffer(context->device, context->transfer_buffer, false);
    SDL_memcpy(transfer_data, context->geometry_data.vertices, sizeof(Vertex) * context->geometry_data.vertex_count);
    SDL_memcpy(transfer_data + MAX_VERTEX_COUNT, context->geometry_data.indices, sizeof(int) * context->geometry_data.index_count);
    SDL_UnmapGPUTransferBuffer(context->device, context->transfer_buffer);
}
/**
 * Transfers geometry data from transfer buffer to GPU vertex/index buffers.
 *
 * This function performs the actual GPU memory transfer operation by:
 * 1. Beginning a GPU copy pass (required for all GPU-to-GPU memory operations)
 * 2. Uploading vertex data from transfer buffer to the dedicated vertex buffer
 * 3. Uploading index data from transfer buffer to the dedicated index buffer
 * 4. Ending the copy pass to commit the transfers
 *
 * Transfer operations:
 * - Vertex data: transfer_buffer[0] → vertex_buffer (positions, colors, UVs)
 * - Index data: transfer_buffer[MAX_VERTEX_COUNT offset] → index_buffer (triangle indices)
 *
 * IMPORTANT: This function MUST be called outside of any active render pass!
 * Copy passes and render passes cannot be active simultaneously on the same command buffer.
 *
 * Memory flow: CPU → transfer_buffer (via set_geometry_data) → GPU buffers (via this function)
 *
 * @param context UIText context containing transfer buffer and target GPU buffers
 * @param cmd_buf Command buffer to record the copy operations
 */
static void transfer_data(UITextContext *context, SDL_GPUCommandBuffer *cmd_buf)
{
    SDL_GPUCopyPass *copy_pass = check_error_ptr(SDL_BeginGPUCopyPass(cmd_buf));
    SDL_UploadToGPUBuffer(
            copy_pass,
            &(SDL_GPUTransferBufferLocation){.transfer_buffer = context->transfer_buffer, .offset = 0},
            &(SDL_GPUBufferRegion){.buffer = context->vertex_buffer, .offset = 0, .size = sizeof(Vertex) * context->geometry_data.vertex_count},
            false);
    SDL_UploadToGPUBuffer(
            copy_pass,
            &(SDL_GPUTransferBufferLocation){.transfer_buffer = context->transfer_buffer, .offset = sizeof(Vertex) * MAX_VERTEX_COUNT},
            &(SDL_GPUBufferRegion){.buffer = context->index_buffer, .offset = 0, .size = sizeof(int) * context->geometry_data.index_count},
            false);
    SDL_EndGPUCopyPass(copy_pass);
}



// Public API functions
UITextContext* UIText_CreateContext(SDL_GPUDevice *device, SDL_Window *window, int window_width, int window_height, bool use_SDF)
{
    UITextContext *context = SDL_calloc(1, sizeof(UITextContext));
    if (!context)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate UITextContext");
        return NULL;
    }

    context->device = device;
    context->window_width = window_width;
    context->window_height = window_height;
    context->use_SDF = use_SDF;

    // Load shaders
    SDL_GPUShader *vertex_shader = check_error_ptr(load_shader(device, VertexShader, 0, 1, 0, 0));
    SDL_GPUShader *fragment_shader = check_error_ptr(load_shader(device, use_SDF ? PixelShader_SDF : PixelShader, 1, 0, 0, 0));

    if (!vertex_shader || !fragment_shader)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to load shaders");
        SDL_free(context);
        return NULL;
    }

    // Create graphics pipeline
    SDL_GPUGraphicsPipelineCreateInfo pipeline_create_info = {
            .target_info = {
                    .num_color_targets = 1,
                    .color_target_descriptions = (SDL_GPUColorTargetDescription[]){
                            {//
                             .format = SDL_GetGPUSwapchainTextureFormat(device, window),
                             .blend_state = (SDL_GPUColorTargetBlendState){//
                                     .enable_blend = true,
                                     .alpha_blend_op = SDL_GPU_BLENDOP_ADD,
                                     .color_blend_op = SDL_GPU_BLENDOP_ADD,
                                     .color_write_mask = 0xF,
                                     .src_alpha_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                                     .dst_alpha_blendfactor = SDL_GPU_BLENDFACTOR_DST_ALPHA,
                                     .src_color_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                                     .dst_color_blendfactor = SDL_GPU_BLENDFACTOR_ONE_MINUS_SRC_ALPHA}//
														} //
                    },
                    .has_depth_stencil_target = false,
                    .depth_stencil_format = SDL_GPU_TEXTUREFORMAT_INVALID},
            .vertex_input_state = (SDL_GPUVertexInputState){
                    .num_vertex_buffers = 1,
                    .vertex_buffer_descriptions = (SDL_GPUVertexBufferDescription[]){
                            {//
                             .slot = 0,
                             .input_rate = SDL_GPU_VERTEXINPUTRATE_VERTEX,
                             .pitch = sizeof(Vertex)} //
                    },
                    .num_vertex_attributes = 3, //
                    .vertex_attributes = (SDL_GPUVertexAttribute[]){
                            {//
                             .buffer_slot = 0,
                             .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT3,
                             .location = 0,
                             .offset = 0},
                            {//
                             .buffer_slot = 0,
                             .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT4,
                             .location = 1,
                             .offset = sizeof(float) * 3},
                            {//
                             .buffer_slot = 0,
                             .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT2,
                             .location = 2,
                             .offset = sizeof(float) * 7} //
                    } //
            },
            .primitive_type = SDL_GPU_PRIMITIVETYPE_TRIANGLELIST,
            .vertex_shader = vertex_shader,
            .fragment_shader = fragment_shader};
    context->pipeline = check_error_ptr(SDL_CreateGPUGraphicsPipeline(device, &pipeline_create_info));

    SDL_ReleaseGPUShader(device, vertex_shader);
    SDL_ReleaseGPUShader(device, fragment_shader);

    if (!context->pipeline)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create graphics pipeline");
        SDL_free(context);
        return NULL;
    }

    // Create buffers
    context->vertex_buffer = check_error_ptr(SDL_CreateGPUBuffer(device, &(SDL_GPUBufferCreateInfo){.usage = SDL_GPU_BUFFERUSAGE_VERTEX, .size = sizeof(Vertex) * MAX_VERTEX_COUNT}));
    context->index_buffer = check_error_ptr(SDL_CreateGPUBuffer(device, &(SDL_GPUBufferCreateInfo){.usage = SDL_GPU_BUFFERUSAGE_INDEX, .size = sizeof(int) * MAX_INDEX_COUNT}));
    context->transfer_buffer = check_error_ptr(SDL_CreateGPUTransferBuffer(device, &(SDL_GPUTransferBufferCreateInfo){.usage = SDL_GPU_TRANSFERBUFFERUSAGE_UPLOAD, .size = (sizeof(Vertex) * MAX_VERTEX_COUNT) + (sizeof(int) * MAX_INDEX_COUNT)}));
    context->sampler = check_error_ptr(SDL_CreateGPUSampler(device, &(SDL_GPUSamplerCreateInfo){.min_filter = SDL_GPU_FILTER_LINEAR, .mag_filter = SDL_GPU_FILTER_LINEAR, .mipmap_mode = SDL_GPU_SAMPLERMIPMAPMODE_LINEAR, .address_mode_u = SDL_GPU_SAMPLERADDRESSMODE_CLAMP_TO_EDGE, .address_mode_v = SDL_GPU_SAMPLERADDRESSMODE_CLAMP_TO_EDGE, .address_mode_w = SDL_GPU_SAMPLERADDRESSMODE_CLAMP_TO_EDGE}));

    if (!context->vertex_buffer || !context->index_buffer || !context->transfer_buffer || !context->sampler)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create GPU resources");
        UIText_DestroyContext(context);
        return NULL;
    }

    // Allocate geometry data
    context->geometry_data.vertices = SDL_calloc(MAX_VERTEX_COUNT, sizeof(Vertex));
    context->geometry_data.indices = SDL_calloc(MAX_INDEX_COUNT, sizeof(int));

    if (!context->geometry_data.vertices || !context->geometry_data.indices)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate geometry data");
        UIText_DestroyContext(context);
        return NULL;
    }

    // Initialize TTF engine
    context->engine = check_error_ptr(TTF_CreateGPUTextEngine(device));
    if (!context->engine)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create TTF GPU text engine");
        UIText_DestroyContext(context);
        return NULL;
    }

    // Setup orthographic projection matrix for 2D rendering
    UIText_UpdateProjectionMatrix(context, window_width, window_height);

    return context;
}

void UIText_DestroyContext(UITextContext *context)
{
    if (!context)
        return;

    if (context->geometry_data.vertices)
        SDL_free(context->geometry_data.vertices);
    if (context->geometry_data.indices)
        SDL_free(context->geometry_data.indices);

    if (context->engine)
        TTF_DestroyGPUTextEngine(context->engine);

    if (context->device)
    {
        if (context->transfer_buffer)
            SDL_ReleaseGPUTransferBuffer(context->device, context->transfer_buffer);
        if (context->sampler)
            SDL_ReleaseGPUSampler(context->device, context->sampler);
        if (context->vertex_buffer)
            SDL_ReleaseGPUBuffer(context->device, context->vertex_buffer);
        if (context->index_buffer)
            SDL_ReleaseGPUBuffer(context->device, context->index_buffer);
        if (context->pipeline)
            SDL_ReleaseGPUGraphicsPipeline(context->device, context->pipeline);
    }

    SDL_free(context);
}

UITextObject* UIText_CreateText(UITextContext *context, TTF_Font *font, const char *text_string, float x, float y, SDL_FColor colour)
{
    if (!context || !font || !text_string)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Invalid parameters for UIText_CreateText");
        return NULL;
    }

    UITextObject *text_obj = SDL_calloc(1, sizeof(UITextObject));
    if (!text_obj)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate UITextObject");
        return NULL;
    }

    // Set SDF mode on the font to match context
    TTF_SetFontSDF(font, context->use_SDF);

    // Create the text object at origin (0,0)
    text_obj->text = check_error_ptr(TTF_CreateText(context->engine, font, text_string, 0));
    if (!text_obj->text)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create TTF text");
        SDL_free(text_obj);
        return NULL;
    }

    text_obj->font = font;
    text_obj->colour = colour;
    text_obj->x = x;
    text_obj->y = y;
    text_obj->letter_spacing = 1.0f; // Default: normal spacing

    // Initialize individual geometry data
    text_obj->geometry_data.vertices = SDL_malloc(MAX_VERTEX_COUNT * sizeof(Vertex));
    text_obj->geometry_data.indices = SDL_malloc(MAX_INDEX_COUNT * sizeof(int));
    text_obj->geometry_data.vertex_count = 0;
    text_obj->geometry_data.index_count = 0;
    text_obj->geometry_dirty = true; // Mark as needing geometry calculation

    if (!text_obj->geometry_data.vertices || !text_obj->geometry_data.indices) {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate geometry data for text object");
        if (text_obj->geometry_data.vertices) SDL_free(text_obj->geometry_data.vertices);
        if (text_obj->geometry_data.indices) SDL_free(text_obj->geometry_data.indices);
        TTF_DestroyText(text_obj->text);
        SDL_free(text_obj);
        return NULL;
    }

    // Initialize total_width with the original text width
    int text_width, text_height;
    if (TTF_GetTextSize(text_obj->text, &text_width, &text_height)) {
        text_obj->total_width = (float)text_width;
    } else {
        text_obj->total_width = 0.0f; // Fallback if size calculation fails
    }

    return text_obj;
}

void UIText_DestroyText(UITextObject *text_obj)
{
    if (!text_obj)
        return;

    if (text_obj->text)
        TTF_DestroyText(text_obj->text);

    // Clean up individual geometry data
    if (text_obj->geometry_data.vertices)
        SDL_free(text_obj->geometry_data.vertices);
    if (text_obj->geometry_data.indices)
        SDL_free(text_obj->geometry_data.indices);

    SDL_free(text_obj);
}

// Calculate accurate text width using individual character metrics and letter spacing
static float CalculateTextWidthWithSpacing(UITextObject *text_obj)
{
    if (!text_obj || !text_obj->text || !text_obj->font)
        return 0.0f;

    const char *text_string = text_obj->text->text;
    if (!text_string || strlen(text_string) == 0)
        return 0.0f;

    float total_width = 0.0f;
    int text_length = strlen(text_string);

    // Calculate width character by character
    for (int i = 0; i < text_length; i++) {
        char single_char[2] = {text_string[i], '\0'};

        // Get the width of this individual character
        int char_width, char_height;
        if (TTF_GetStringSize(text_obj->font, single_char, 0, &char_width, &char_height)) {
            total_width += (float)char_width;

            // Add letter spacing between characters (except after the last character)
            if (i < text_length - 1) {
                // Calculate the base spacing between this character and the next
                // We use a simple approach: average character width * spacing multiplier
                float base_spacing = (float)char_width * 0.1f; // Approximate base spacing
                float adjusted_spacing = base_spacing * text_obj->letter_spacing;
                total_width += adjusted_spacing;
            }
        }
    }

    return total_width;
}

// Process individual text object geometry with letter spacing
static void ProcessTextObjectGeometry(UITextObject *text_obj, TTF_GPUAtlasDrawSequence *draw_sequence)
{
    if (!text_obj || !draw_sequence)
        return;

    // Count total character quads across all sequences
    int total_chars = 0;
    for (TTF_GPUAtlasDrawSequence *seq = draw_sequence; seq != NULL; seq = seq->next) {
        total_chars += seq->num_vertices / 4;  // 4 vertices per character
    }

    if (total_chars == 0) return;  // No characters to process

    // Collect character positions (left edge of each character quad)
    float *char_positions = SDL_malloc(total_chars * sizeof(float));
    if (!char_positions) {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate character positions array");
        return;
    }

    // First pass: collect original character positions
    int char_index = 0;
    for (TTF_GPUAtlasDrawSequence *seq = draw_sequence; seq != NULL; seq = seq->next) {
        int num_chars_in_seq = seq->num_vertices / 4;
        for (int c = 0; c < num_chars_in_seq; c++) {
            // Find the actual leftmost X coordinate among all 4 vertices of this character
            int vertex_base = c * 4;
            float leftmost_x = seq->xy[vertex_base].x;
            for (int v = 1; v < 4; v++) {
                if (seq->xy[vertex_base + v].x < leftmost_x) {
                    leftmost_x = seq->xy[vertex_base + v].x;
                }
            }
            char_positions[char_index] = leftmost_x;
            char_index++;
        }
    }

    // Calculate spacing adjustments
    float *spacing_offsets = SDL_malloc(total_chars * sizeof(float));
    if (!spacing_offsets) {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate spacing offsets array");
        SDL_free(char_positions);
        return;
    }

    // Calculate cumulative spacing offsets for left alignment
    spacing_offsets[0] = 0.0f; // First character stays fixed
    for (int i = 1; i < total_chars; i++) {
        // Calculate the original spacing between this character and the previous one
        float original_spacing = char_positions[i] - char_positions[i-1];

        // Apply letter spacing multiplier
        float adjusted_spacing = original_spacing * text_obj->letter_spacing;
        float spacing_delta = adjusted_spacing - original_spacing;

        // Accumulate spacing offset
        spacing_offsets[i] = spacing_offsets[i-1] + spacing_delta;
    }

    // Second pass: apply spacing offsets to vertices and store in text object's geometry
    char_index = 0;
    for (TTF_GPUAtlasDrawSequence *seq = draw_sequence; seq != NULL; seq = seq->next)
    {
        int num_chars_in_seq = seq->num_vertices / 4;
        for (int c = 0; c < num_chars_in_seq && char_index < total_chars; c++) {
            // Get the spacing offset for this character
            float x_offset = spacing_offsets[char_index];

            // Apply the offset to all 4 vertices of this character
            int vertex_base = c * 4;
            for (int v = 0; v < 4; v++) {
                Vertex vert;
                const SDL_FPoint pos = seq->xy[vertex_base + v];

                // Apply spacing offset while preserving character shape
                vert.pos = (Vec3){pos.x + x_offset, pos.y, 0.0f};
                vert.colour = text_obj->colour;
                vert.uv = seq->uv[vertex_base + v];

                text_obj->geometry_data.vertices[text_obj->geometry_data.vertex_count + vertex_base + v] = vert;
            }
            char_index++;
        }

        // Copy indices with proper offset
        for (int idx = 0; idx < seq->num_indices; idx++) {
            text_obj->geometry_data.indices[text_obj->geometry_data.index_count + idx] =
                seq->indices[idx] + text_obj->geometry_data.vertex_count;
        }

        text_obj->geometry_data.vertex_count += seq->num_vertices;
        text_obj->geometry_data.index_count += seq->num_indices;
    }

    // Clean up allocated memory
    SDL_free(char_positions);
    SDL_free(spacing_offsets);
}

// Accumulate text object geometry into global geometry for rendering
static void AccumulateTextGeometry(UITextContext *context, UITextObject *text_obj)
{
    if (!context || !text_obj)
        return;

    // Copy vertices with proper index offset
    for (int i = 0; i < text_obj->geometry_data.vertex_count; i++) {
        context->geometry_data.vertices[context->geometry_data.vertex_count + i] =
            text_obj->geometry_data.vertices[i];
    }

    // Copy indices with proper vertex offset
    for (int i = 0; i < text_obj->geometry_data.index_count; i++) {
        context->geometry_data.indices[context->geometry_data.index_count + i] =
            text_obj->geometry_data.indices[i] + context->geometry_data.vertex_count;
    }

    context->geometry_data.vertex_count += text_obj->geometry_data.vertex_count;
    context->geometry_data.index_count += text_obj->geometry_data.index_count;
}


// Prepare text data for rendering (call outside render pass)
void UIText_PrepareTextData(UITextContext *context, UITextObject **text_objects, int count, SDL_GPUCommandBuffer *cmd_buf)
{
    if (!context || !text_objects || count <= 0 || !cmd_buf)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Invalid parameters for UIText_PrepareTextData");
        return;
    }

    // Set up projection matrix for bottom-left origin coordinate system (required for TTF text rendering)
    // TTF text glyphs are designed for bottom-left origin, so we use the standard OpenGL projection
    context->projection_matrix = SDL_MatrixOrtho(0.0f, (float)context->window_width, 0.0f, (float)context->window_height, -1.0f, 1.0f);

    // Reset global geometry data for combined rendering
    context->geometry_data.vertex_count = 0;
    context->geometry_data.index_count = 0;
    context->model_matrix_count = 0;

    // Process each text object individually
    for (int i = 0; i < count && i < (MAX_VERTEX_COUNT / 4); i++)
    {
        UITextObject *text_obj = text_objects[i];
        if (!text_obj || !text_obj->text)
            continue;

        // Calculate accurate text width with letter spacing
        text_obj->total_width = CalculateTextWidthWithSpacing(text_obj);

        // Create model matrix with text object position
        // Convert from top-left UI coordinates to bottom-left coordinates for TTF rendering
        float converted_y = (float)context->window_height - text_obj->y;
        SDL_Mat4X4 model = SDL_MatrixTranslation((SDL_Vec3){text_obj->x, converted_y, 0.0f});

        context->model_matrices[context->model_matrix_count] = model;
        context->model_matrix_count++;

        // Process individual text object geometry if dirty or letter spacing changed
        if (text_obj->geometry_dirty) {
            // Reset this text object's geometry data
            text_obj->geometry_data.vertex_count = 0;
            text_obj->geometry_data.index_count = 0;

            // Get the draw data for this text
            TTF_GPUAtlasDrawSequence *draw_sequence = TTF_GetGPUTextDrawData(text_obj->text);
            if (!draw_sequence) {
                continue;
            }

            // Process letter spacing for this text object's individual geometry
            ProcessTextObjectGeometry(text_obj, draw_sequence);

            // Mark geometry as clean
            text_obj->geometry_dirty = false;
        }

        // Accumulate this text object's geometry into the global geometry for rendering
        AccumulateTextGeometry(context, text_obj);
    }

    // Transfer combined data to GPU (must be done outside render pass)
    if (context->geometry_data.vertex_count > 0)
    {
        set_geometry_data(context);
        transfer_data(context, cmd_buf);
    }
}

// Render prepared text within an existing render pass
void UIText_RenderInRenderPass(UITextContext *context, UITextObject **text_objects, int count,
                               SDL_GPURenderPass *render_pass, SDL_GPUCommandBuffer *cmd_buf)
{
    if (!context || !text_objects || count <= 0 || !render_pass || !cmd_buf)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Invalid parameters for UIText_RenderInRenderPass");
        return;
    }

    // Only render if we have geometry data
    if (context->geometry_data.vertex_count <= 0)
    {
        return;
    }

    // Bind text rendering pipeline and resources
    SDL_BindGPUGraphicsPipeline(render_pass, context->pipeline);
    SDL_BindGPUVertexBuffers(render_pass, 0, &(SDL_GPUBufferBinding){.buffer = context->vertex_buffer, .offset = 0}, 1);
    SDL_BindGPUIndexBuffer(render_pass, &(SDL_GPUBufferBinding){.buffer = context->index_buffer, .offset = 0}, SDL_GPU_INDEXELEMENTSIZE_32BIT);

    // Draw each text object with its individual model matrix
    int index_offset = 0, vertex_offset = 0;
    int matrix_index = 0;

    for (int i = 0; i < count; i++)
    {
        UITextObject *text_obj = text_objects[i];
        if (!text_obj || !text_obj->text)
            continue;

        TTF_GPUAtlasDrawSequence *draw_sequence = TTF_GetGPUTextDrawData(text_obj->text);
        if (!draw_sequence)
            continue;

        // Push individual matrices for this text object (projection + model)
        if (matrix_index < context->model_matrix_count) {
            SDL_Mat4X4 matrices[2] = {
                context->projection_matrix,
                context->model_matrices[matrix_index]
            };
            SDL_PushGPUVertexUniformData(cmd_buf, 0, matrices, sizeof(SDL_Mat4X4) * 2);
            matrix_index++;
        }

        for (TTF_GPUAtlasDrawSequence *seq = draw_sequence; seq != NULL; seq = seq->next)
        {
            SDL_BindGPUFragmentSamplers(
                    render_pass, 0,
                    &(SDL_GPUTextureSamplerBinding){
                            .texture = seq->atlas_texture,
                            .sampler = context->sampler},
                    1);
            SDL_DrawGPUIndexedPrimitives(render_pass, seq->num_indices, 1, index_offset, vertex_offset, 0);
            index_offset += seq->num_indices;
            vertex_offset += seq->num_vertices;
        }
    }
}



// Utility functions
void UIText_SetTextPosition(UITextObject *text_obj, float x, float y)
{
    if (text_obj)
    {
        text_obj->x = x;
        text_obj->y = y;
    }
}

void UIText_SetTextColour(UITextObject *text_obj, SDL_FColor colour)
{
    if (text_obj)
    {
        text_obj->colour = colour;
    }
}

void UIText_SetLetterSpacing(UITextObject *text_obj, float spacing)
{
    if (text_obj)
    {
        text_obj->letter_spacing = spacing;

        // Mark geometry as dirty to trigger recalculation
        text_obj->geometry_dirty = true;

        // Recalculate text width using accurate character-by-character calculation
        text_obj->total_width = CalculateTextWidthWithSpacing(text_obj);

        // For left alignment, no position adjustment is needed
        // The text position (x, y) represents the left edge, which should stay fixed
        // If you need center or right alignment, you would adjust text_obj->x here
        // based on the difference between old and new total_width
    }
}



void UIText_UpdateProjectionMatrix(UITextContext *context, int window_width, int window_height)
{
    if (!context)
        return;

    context->window_width = window_width;
    context->window_height = window_height;

    // Setup orthographic projection matrix for 2D rendering with top-left origin
    // For top-left origin: left=0, right=width, top=0, bottom=height
    context->projection_matrix = SDL_MatrixOrtho(0.0f, (float)window_width, (float)window_height, 0.0f, -1.0f, 1.0f);
}